from datetime import datetime

from sqlalchemy import Column, DateTime, String, BigInteger, DECIMAL, Index

from config.database import Base


class TechnicalManualPrice(Base):
    """
    技术手册价格表
    """

    __tablename__ = "technical_manual_price"

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="主键ID")

    # 唯一标识字段
    method = Column(String(100), nullable=False, comment="检测方法")
    category_code = Column(String(20), nullable=False, comment="类目编号")
    # 保留原字段用于兼容性（后续可删除）
    # category = Column(String(50), nullable=True, comment="检测类别（已废弃）")
    # classification = Column(String(50), nullable=True, comment="分类（已废弃）")

    # 检测价格相关字段
    first_item_price = Column(DECIMAL(10, 2), nullable=True, comment="检测首项单价")
    additional_item_price = Column(DECIMAL(10, 2), nullable=True, comment="检测增项单价")
    testing_fee_limit = Column(DECIMAL(10, 2), nullable=True, comment="检测费上限")

    # 采集价格相关字段
    sampling_price = Column(DECIMAL(10, 2), nullable=True, comment="采集单价")
    pretreatment_price = Column(DECIMAL(10, 2), nullable=True, comment="前处理单价")

    # 其他价格相关字段
    # special_consumables_price 字段已迁移到技术手册表中

    # 系统字段
    status = Column(String(1), default="0", comment="状态（0正常 1停用）")
    create_by = Column(String(64), default="", comment="创建者")
    create_time = Column(DateTime, default=datetime.now(), comment="创建时间")
    update_by = Column(String(64), default="", comment="更新者")
    update_time = Column(DateTime, default=datetime.now(), comment="更新时间")
    remark = Column(String(500), default="", comment="备注")

    # 创建唯一索引
    __table_args__ = (Index("idx_technical_manual_price_unique", "method", "category_code", unique=True),)
