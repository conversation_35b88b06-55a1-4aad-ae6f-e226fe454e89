"""
采样任务执行人指派控制器
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_sampling.service.task_assignment_service import TaskAssignmentService
from module_sampling.dto.sampling_task_assignment_dto import (
    TaskAssignmentRequestDTO,
    SamplingTaskAssignmentDTO
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from utils.response_util import ResponseUtil
from utils.log_util import logger
from utils.common_util import CamelCaseUtil


router = APIRouter(prefix="/sampling/executor-assignment", tags=["采样任务执行人指派"])


@router.post("/assign", summary="创建或更新任务指派")
async def assign_task(
    request: TaskAssignmentRequestDTO,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    创建或更新任务指派
    
    Args:
        request: 任务指派请求数据
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        指派结果
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 开始创建或更新任务指派")

        service = TaskAssignmentService(db)
        assignments = await service.create_or_update_assignments(request, current_user.user.user_id)
        
        logger.info(f"任务指派操作成功，共处理 {len(assignments)} 个指派")
        return ResponseUtil.success(data=assignments, msg="任务指派成功")
        
    except Exception as e:
        logger.error(f"任务指派失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task/{task_id}", summary="获取任务的所有指派")
async def get_task_assignments(
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取任务的所有指派
    
    Args:
        task_id: 任务ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        任务指派列表
    """
    try:
        service = TaskAssignmentService(db)
        assignments = await service.get_assignments_by_task_id(task_id)
        
        return ResponseUtil.success(data=assignments)
        
    except Exception as e:
        logger.error(f"获取任务指派失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/user/{user_id}", summary="获取用户的所有指派")
async def get_user_assignments(
    user_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取用户的所有指派

    Args:
        user_id: 用户ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        用户指派列表
    """
    try:
        service = TaskAssignmentService(db)
        assignments = await service.get_execution_list_by_user_id(user_id)

        return ResponseUtil.success(data=assignments)

    except Exception as e:
        logger.error(f"获取用户指派失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/executions/{assignment_id}", summary="获取执行任务详情")
async def get_execution_task_detail(
    assignment_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取执行任务详情，包括过滤后的周期条目
    
    Args:
        assignment_id: 指派ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        执行任务详情
    """
    try:
        service = TaskAssignmentService(db)
        execution_detail = await service.get_execution_task_detail(assignment_id)
        
        # 将字段名从蛇形命名转换为驼峰命名
        camel_detail = CamelCaseUtil.transform_result(execution_detail)
        
        return ResponseUtil.success(data=camel_detail)
        
    except Exception as e:
        logger.error(f"获取执行任务详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/all-executions", summary="获取所有执行任务", dependencies=[Depends(CheckUserInterfaceAuth('assignment-execution:all'))])
async def get_all_executions(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取所有执行任务（需要assignment-execution:all权限）

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        所有执行任务列表
    """
    try:
        service = TaskAssignmentService(db)
        assignments = await service.get_all_execution_list()
        
        # 将字段名从蛇形命名转换为驼峰命名
        camel_assignments = CamelCaseUtil.transform_result(assignments)

        return ResponseUtil.success(data=camel_assignments)

    except Exception as e:
        logger.error(f"获取所有执行任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{assignment_id}", summary="删除任务指派")
async def delete_assignment(
    assignment_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    删除任务指派
    
    Args:
        assignment_id: 指派ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        删除结果
    """
    try:
        service = TaskAssignmentService(db)
        result = await service.delete_assignment(assignment_id)
        
        if result:
            return ResponseUtil.success(msg="删除成功")
        else:
            return ResponseUtil.error(msg="删除失败，指派不存在")
            
    except Exception as e:
        logger.error(f"删除任务指派失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
