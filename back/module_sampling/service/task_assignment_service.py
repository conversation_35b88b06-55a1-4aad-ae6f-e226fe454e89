"""
采样任务执行人指派服务
"""

import json
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from module_sampling.dao.sampling_task_assignment_dao import SamplingTaskAssignmentDAO
from module_sampling.entity.do.sampling_task_assignment_do import SamplingTaskAssignment
from module_sampling.dto.sampling_task_assignment_dto import (
    SamplingTaskAssignmentCreateDTO,
    SamplingTaskAssignmentUpdateDTO,
    SamplingTaskAssignmentDTO,
    TaskAssignmentRequestDTO
)
from module_admin.entity.do.user_do import SysUser
from exceptions.exception import ServiceException


class TaskAssignmentService:
    """采样任务执行人指派服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.assignment_dao = SamplingTaskAssignmentDAO(db)
    
    async def create_or_update_assignments(
        self,
        request: TaskAssignmentRequestDTO,
        current_user_id: int
    ) -> List[SamplingTaskAssignmentDTO]:
        """创建或更新执行人指派"""
        try:
            result_assignments = []
            
            for assignment_data in request.assignments:
                # 检查是否已存在相同分组的指派
                existing_assignment = await self.assignment_dao.get_assignment_by_group_key(
                    task_id=assignment_data.sampling_task_id,
                    cycle_number=assignment_data.cycle_number,
                    cycle_type=assignment_data.cycle_type,
                    detection_category=assignment_data.detection_category,
                    point_name=assignment_data.point_name
                )
                
                if existing_assignment:
                    # 更新现有指派
                    existing_assignment.assigned_user_ids = json.dumps(assignment_data.assigned_user_ids)
                    existing_assignment.update_by = current_user_id
                    updated_assignment = await self.assignment_dao.update_assignment(existing_assignment)
                    result_assignments.append(await self._convert_to_dto(updated_assignment))
                else:
                    # 创建新指派
                    new_assignment = SamplingTaskAssignment(
                        sampling_task_id=assignment_data.sampling_task_id,
                        cycle_number=assignment_data.cycle_number,
                        cycle_type=assignment_data.cycle_type,
                        detection_category=assignment_data.detection_category,
                        point_name=assignment_data.point_name,
                        assigned_user_ids=json.dumps(assignment_data.assigned_user_ids),
                        create_by=current_user_id,
                        update_by=current_user_id
                    )
                    created_assignment = await self.assignment_dao.create_assignment(new_assignment)
                    result_assignments.append(await self._convert_to_dto(created_assignment))
            
            await self.db.commit()
            return result_assignments
            
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"创建或更新任务指派失败: {str(e)}")
    
    async def get_assignments_by_task_id(self, task_id: int) -> List[SamplingTaskAssignmentDTO]:
        """根据任务ID获取所有指派"""
        assignments = await self.assignment_dao.get_assignments_by_task_id(task_id)
        return [await self._convert_to_dto(assignment) for assignment in assignments]
    
    async def get_assignments_by_user_id(self, user_id: int) -> List[SamplingTaskAssignmentDTO]:
        """根据用户ID获取分配给该用户的任务指派"""
        assignments = await self.assignment_dao.get_assignments_by_user_id(user_id)
        return [await self._convert_to_dto(assignment) for assignment in assignments]
    
    async def delete_assignment(self, assignment_id: int) -> bool:
        """删除任务指派"""
        try:
            result = await self.assignment_dao.delete_assignment(assignment_id)
            await self.db.commit()
            return result
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"删除任务指派失败: {str(e)}")
    
    async def _convert_to_dto(self, assignment: SamplingTaskAssignment) -> SamplingTaskAssignmentDTO:
        """将实体转换为DTO"""
        # 解析分配的用户ID
        assigned_user_ids = []
        if assignment.assigned_user_ids:
            try:
                assigned_user_ids = json.loads(assignment.assigned_user_ids)
            except:
                assigned_user_ids = []

        # 获取用户名称
        assigned_user_names = []
        if assigned_user_ids:
            stmt = select(SysUser.nick_name).where(SysUser.user_id.in_(assigned_user_ids))
            result = await self.db.execute(stmt)
            assigned_user_names = [row[0] for row in result.fetchall()]

        return SamplingTaskAssignmentDTO(
            id=assignment.id,
            sampling_task_id=assignment.sampling_task_id,
            cycle_number=assignment.cycle_number,
            cycle_type=assignment.cycle_type,
            detection_category=assignment.detection_category,
            point_name=assignment.point_name,
            assigned_user_ids=assigned_user_ids,
            assigned_user_names=assigned_user_names,
            create_by=assignment.create_by,
            create_time=assignment.create_time,
            update_by=assignment.update_by,
            update_time=assignment.update_time
        )

    async def get_execution_list_by_user_id(self, user_id: int) -> List[dict]:
        """根据用户ID获取执行任务列表（包含任务详细信息）"""
        from module_sampling.entity.do.sampling_task_do import SamplingTask
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_customer.entity.do.customer_do import Customer

        assignments = await self.assignment_dao.get_assignments_by_user_id(user_id)
        execution_list = []

        for assignment in assignments:
            # 获取任务详细信息
            stmt = select(SamplingTask).options(
                selectinload(SamplingTask.project_quotation).selectinload(ProjectQuotation.customer)
            ).where(SamplingTask.id == assignment.sampling_task_id)
            result = await self.db.execute(stmt)
            task = result.scalar_one_or_none()

            if task:
                # 解析分配的用户ID和名称
                assigned_user_ids = []
                assigned_user_names = []
                if assignment.assigned_user_ids:
                    try:
                        assigned_user_ids = json.loads(assignment.assigned_user_ids)
                        if assigned_user_ids:
                            user_stmt = select(SysUser.nick_name).where(SysUser.user_id.in_(assigned_user_ids))
                            user_result = await self.db.execute(user_stmt)
                            assigned_user_names = [row[0] for row in user_result.fetchall()]
                    except:
                        pass

                execution_item = {
                    'id': assignment.id,
                    'sampling_task_id': assignment.sampling_task_id,
                    'task_code': task.task_code,
                    'task_name': task.task_name,
                    'status': task.status,
                    'planned_start_date': task.planned_start_date,
                    'planned_end_date': task.planned_end_date,
                    'actual_start_date': task.actual_start_date,
                    'actual_end_date': task.actual_end_date,
                    'cycle_number': assignment.cycle_number,
                    'cycle_type': assignment.cycle_type,
                    'detection_category': assignment.detection_category,
                    'point_name': assignment.point_name,
                    'assigned_user_ids': assigned_user_ids,
                    'assigned_user_names': assigned_user_names,
                    'project_name': task.project_quotation.project_name if task.project_quotation else '',
                    'customer_name': task.project_quotation.customer.customer_name if task.project_quotation and task.project_quotation.customer else '',
                    'create_time': assignment.create_time,
                    'update_time': assignment.update_time
                }
                execution_list.append(execution_item)

        return execution_list

    async def get_execution_task_detail(self, assignment_id: int) -> dict:
        """获取执行任务详情，包括过滤后的周期条目"""
        from module_sampling.entity.do.sampling_task_do import SamplingTask
        from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
        from module_sampling.entity.do.sampling_task_cycle_item_do import SamplingTaskCycleItem
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
        from module_sampling.dto.detection_cycle_item_dto import DetectionCycleItemDTO
        
        # 获取执行任务指派信息
        assignment = await self.assignment_dao.get_assignment_by_id(assignment_id)
        if not assignment:
            raise ServiceException(message="执行任务不存在")
        
        # 获取采样任务信息
        stmt = select(SamplingTask).options(
            selectinload(SamplingTask.project_quotation)
        ).where(SamplingTask.id == assignment.sampling_task_id)
        result = await self.db.execute(stmt)
        task = result.scalar_one_or_none()
        
        if not task:
            raise ServiceException(message="采样任务不存在")
        
        # 获取该执行任务相关的周期条目（根据cycle_number、cycle_type、detection_category、point_name过滤）
        cycle_items_query = (
            select(DetectionCycleItem)
            .join(SamplingTaskCycleItem, DetectionCycleItem.id == SamplingTaskCycleItem.detection_cycle_item_id)
            .join(ProjectQuotationItem, DetectionCycleItem.project_quotation_item_id == ProjectQuotationItem.id)
            .where(
                SamplingTaskCycleItem.sampling_task_id == assignment.sampling_task_id,
                DetectionCycleItem.cycle_number == assignment.cycle_number
            )
        )
        
        # 根据执行任务的分组条件进一步过滤
        if assignment.cycle_type:
            cycle_items_query = cycle_items_query.where(ProjectQuotationItem.cycle_type == assignment.cycle_type)
        if assignment.detection_category:
            cycle_items_query = cycle_items_query.where(ProjectQuotationItem.category == assignment.detection_category)
        if assignment.point_name:
            cycle_items_query = cycle_items_query.where(ProjectQuotationItem.point_name == assignment.point_name)
        
        cycle_items_result = await self.db.execute(cycle_items_query)
        cycle_items = cycle_items_result.scalars().all()
        
        # 转换周期条目为DTO
        cycle_items_dto = []
        for cycle_item in cycle_items:
            # 获取项目报价明细信息
            item_stmt = select(ProjectQuotationItem).where(ProjectQuotationItem.id == cycle_item.project_quotation_item_id)
            item_result = await self.db.execute(item_stmt)
            quotation_item = item_result.scalar_one_or_none()
            
            cycle_item_dto = DetectionCycleItemDTO(
                id=cycle_item.id,
                project_quotation_id=cycle_item.project_quotation_id,
                project_quotation_item_id=cycle_item.project_quotation_item_id,
                cycle_number=cycle_item.cycle_number,
                status=cycle_item.status,
                status_label=cycle_item.status_label
            )
            
            if quotation_item:
                cycle_item_dto.detection_qualification = quotation_item.qualification_code
                cycle_item_dto.detection_classification = quotation_item.classification
                cycle_item_dto.detection_category = quotation_item.category
                cycle_item_dto.detection_parameter = quotation_item.parameter
                cycle_item_dto.detection_method = quotation_item.method
                cycle_item_dto.sample_source = quotation_item.sample_source
                cycle_item_dto.point_name = quotation_item.point_name
                cycle_item_dto.cycle_type = quotation_item.cycle_type
            
            cycle_items_dto.append(cycle_item_dto)
        
        # 解析分配的用户ID和名称
        assigned_user_ids = []
        assigned_user_names = []
        if assignment.assigned_user_ids:
            try:
                assigned_user_ids = json.loads(assignment.assigned_user_ids)
                if assigned_user_ids:
                    user_stmt = select(SysUser.nick_name).where(SysUser.user_id.in_(assigned_user_ids))
                    user_result = await self.db.execute(user_stmt)
                    assigned_user_names = [row[0] for row in user_result.fetchall()]
            except:
                pass
        
        # 构建返回结果
        return {
            'id': assignment.id,
            'sampling_task_id': assignment.sampling_task_id,
            'task_code': task.task_code,
            'task_name': task.task_name,
            'description': task.description,
            'status': task.status,
            'planned_start_date': task.planned_start_date,
            'planned_end_date': task.planned_end_date,
            'actual_start_date': task.actual_start_date,
            'actual_end_date': task.actual_end_date,
            'cycle_number': assignment.cycle_number,
            'cycle_type': assignment.cycle_type,
            'detection_category': assignment.detection_category,
            'point_name': assignment.point_name,
            'assigned_user_ids': assigned_user_ids,
            'assigned_user_names': assigned_user_names,
            'project_name': task.project_quotation.project_name if task.project_quotation else '',
            'cycle_items': [item.dict() for item in cycle_items_dto],
            'create_time': assignment.create_time,
            'update_time': assignment.update_time
        }

    async def get_all_execution_list(self) -> List[dict]:
        """获取所有执行任务列表（仅超级管理员）"""
        from module_sampling.entity.do.sampling_task_do import SamplingTask
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_customer.entity.do.customer_do import Customer

        # 查询所有执行人指派记录
        stmt = (
            select(SamplingTaskAssignment, SamplingTask, ProjectQuotation, Customer)
            .join(SamplingTask, SamplingTaskAssignment.sampling_task_id == SamplingTask.id)
            .join(ProjectQuotation, SamplingTask.project_quotation_id == ProjectQuotation.id, isouter=True)
            .join(Customer, ProjectQuotation.customer_id == Customer.customer_id, isouter=True)
            .order_by(SamplingTaskAssignment.create_time.desc())
        )

        result = await self.db.execute(stmt)
        records = result.fetchall()

        execution_list = []
        for assignment, task, project_quotation, customer in records:
            # 解析执行人ID和名称
            assigned_user_ids = []
            assigned_user_names = []
            if assignment.assigned_user_ids:
                try:
                    assigned_user_ids = json.loads(assignment.assigned_user_ids)
                    if assigned_user_ids:
                        user_stmt = select(SysUser.nick_name).where(SysUser.user_id.in_(assigned_user_ids))
                        user_result = await self.db.execute(user_stmt)
                        assigned_user_names = [row[0] for row in user_result.fetchall()]
                except:
                    pass

            execution_item = {
                'id': assignment.id,
                'sampling_task_id': assignment.sampling_task_id,
                'task_code': task.task_code,
                'task_name': task.task_name,
                'status': task.status,
                'planned_start_date': task.planned_start_date,
                'planned_end_date': task.planned_end_date,
                'actual_start_date': task.actual_start_date,
                'actual_end_date': task.actual_end_date,
                'cycle_number': assignment.cycle_number,
                'cycle_type': assignment.cycle_type,
                'detection_category': assignment.detection_category,
                'point_name': assignment.point_name,
                'assigned_user_ids': assigned_user_ids,
                'assigned_user_names': assigned_user_names,
                'project_name': project_quotation.project_name if project_quotation else '',
                'customer_name': customer.customer_name if customer else '',
                'create_time': assignment.create_time,
                'update_time': assignment.update_time
            }
            execution_list.append(execution_item)

        return execution_list
