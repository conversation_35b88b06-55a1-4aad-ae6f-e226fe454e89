"""
采样任务执行人指派DTO
"""

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel
from typing import List, Optional
from datetime import datetime


class SamplingTaskAssignmentCreateDTO(BaseModel):
    """创建采样任务执行人指派DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    sampling_task_id: int
    cycle_number: int
    cycle_type: Optional[str] = None
    detection_category: Optional[str] = None
    point_name: Optional[str] = None
    assigned_user_ids: List[int]


class SamplingTaskAssignmentUpdateDTO(BaseModel):
    """更新采样任务执行人指派DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    assigned_user_ids: List[int]


class SamplingTaskAssignmentDTO(BaseModel):
    """采样任务执行人指派DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    id: int
    sampling_task_id: int
    cycle_number: int
    cycle_type: Optional[str] = None
    detection_category: Optional[str] = None
    point_name: Optional[str] = None
    assigned_user_ids: List[int]
    assigned_user_names: Optional[List[str]] = None
    create_by: Optional[int] = None
    create_time: Optional[datetime] = None
    update_by: Optional[int] = None
    update_time: Optional[datetime] = None


class TaskAssignmentRequestDTO(BaseModel):
    """任务执行人指派请求DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    task_id: int
    assignments: List[SamplingTaskAssignmentCreateDTO]
