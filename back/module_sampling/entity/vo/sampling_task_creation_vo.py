#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采样任务分配相关VO模型
"""

from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class CycleItemModel(BaseModel):
    """
    检测周期条目模型
    """
    id: int = Field(..., description="周期条目ID")
    cycle_number: int = Field(..., description="周期序号")
    status: int = Field(..., description="状态：0-未分配，1-已分配，2-已完成")
    status_label: str = Field(..., description="状态标签")
    is_selectable: bool = Field(..., description="是否可选择")


class ProjectQuotationItemCycleModel(BaseModel):
    """
    项目报价明细检测周期模型
    """
    item_id: int = Field(..., description="项目报价明细ID")
    item_code: str = Field(..., description="检测项目编码")
    category: str = Field(..., description="检测类别")
    parameter: str = Field(..., description="检测参数")
    method: str = Field(..., description="检测方法")
    cycle_type: str = Field(..., description="周期类型")
    cycle_count: int = Field(..., description="周期数量")
    cycle_items: List[CycleItemModel] = Field(default_factory=list, description="周期条目列表")


class SamplingTaskCreationModel(BaseModel):
    """
    采样任务创建模型
    """
    project_quotation_id: int = Field(..., alias="projectQuotationId", description="项目报价ID")
    task_name: str = Field(..., alias="taskName", description="任务名称")
    description: Optional[str] = Field(None, alias="taskDescription", description="任务描述")
    selected_cycle_item_ids: List[int] = Field(..., alias="selectedCycleItemIds", description="选中的检测周期条目ID列表")
    planned_start_time: Optional[datetime] = Field(None, alias="plannedStartTime", description="计划开始时间")
    planned_end_time: Optional[datetime] = Field(None, alias="plannedEndTime", description="计划结束时间")
    responsible_user_id: Optional[int] = Field(None, alias="responsibleUserId", description="负责人用户ID")
    remark: Optional[str] = Field(None, description="备注")


class SamplingTaskCreationResponseModel(BaseModel):
    """
    采样任务创建响应模型
    """
    task_id: int = Field(..., description="采样任务ID")
    task_number: str = Field(..., description="任务编号")
    task_name: str = Field(..., description="任务名称")
    cycle_item_count: int = Field(..., description="关联的检测周期条目数量")
    message: str = Field(..., description="创建结果消息")