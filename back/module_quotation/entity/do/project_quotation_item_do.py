"""
项目报价明细数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, DECIMAL, Text, ForeignKey
from sqlalchemy.orm import relationship

from config.database import Base


class ProjectQuotationItem(Base):
    """
    项目报价明细表
    """

    __tablename__ = "project_quotation_item"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    project_quotation_id = Column(Integer, ForeignKey("project_quotation.id"), nullable=False, comment="项目报价ID")
    item_code = Column(String(20), nullable=False, comment="项目编号")

    # 检测信息
    qualification_code = Column(String(50), nullable=True, comment="资质唯一编号")
    classification = Column(String(50), nullable=True, comment="分类")
    category = Column(String(50), nullable=False, comment="检测类别(二级分类)")
    parameter = Column(String(100), nullable=False, comment="检测参数(指标)")
    method = Column(String(200), nullable=False, comment="检测方法")
    limitation_scope = Column(String(200), nullable=True, comment="限制范围")
    # 从技术手册表过来的字段
    special_consumables_price = Column(DECIMAL(10, 2), nullable=True, comment="分析特殊耗材单价")

    # 采样信息
    sample_source = Column(String(50), nullable=True, comment="样品来源")
    point_name = Column(String(100), nullable=True, comment="点位名称")
    point_count = Column(Integer, nullable=False, default=1, comment="点位数")
    cycle_type = Column(String(10), nullable=True, comment="检测周期类型")
    cycle_count = Column(Integer, nullable=True, default=1, comment="检测周期数")
    frequency = Column(Integer, nullable=True, default=1, comment="检测频次数")
    sample_count = Column(Integer, nullable=True, default=1, comment="样品数")
    is_subcontract = Column(String(1), nullable=True, default="0", comment="是否分包(0-否,1-是)")

    # 备注
    remark = Column(Text, nullable=True, comment="备注")

    # 创建人和更新人信息
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")

    # 关联项目报价
    project_quotation = relationship("ProjectQuotation", back_populates="items")
    
    # 关联检测周期条目
    detection_cycle_items = relationship(
        "DetectionCycleItem", back_populates="project_quotation_item", cascade="all, delete-orphan"
    )

    @property
    def sample_unique_key(self):
        """采样信息的唯一key"""
        return (
            f"sample_source:{self.sample_source}/point_name:{self.point_name}/point_count:{self.point_count}"
            f"/cycle_type:{self.cycle_type}/cycle_count:{self.cycle_count}"
            f"/frequency:{self.frequency}/sample_count:{self.sample_count}"
        )
