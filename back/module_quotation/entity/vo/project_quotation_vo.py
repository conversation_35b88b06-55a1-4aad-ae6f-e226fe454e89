"""
项目报价VO模型 - 使用标准Pydantic校验
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel, Field, ConfigDict, field_validator, model_validator
from pydantic.alias_generators import to_camel

from module_quotation.entity.vo.project_quotation_customer_support_vo import ProjectQuotationCustomerSupportModel


class ProjectQuotationItemModel(BaseModel):
    """
    项目报价明细模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: Optional[int] = Field(default=None, description="主键ID")
    item_code: Optional[str] = Field(default=None, description="项目编号", max_length=20)

    # 检测信息
    qualification_code: Optional[str] = Field(default=None, description="资质唯一编号")
    classification: Optional[str] = Field(default=None, description="分类", max_length=50)
    category: str = Field(..., description="检测类别(二级分类)", max_length=50)
    parameter: str = Field(..., description="检测参数(指标)", max_length=100)
    method: str = Field(..., description="检测方法", max_length=200)
    limitation_scope: Optional[str] = Field(default=None, description="限制范围", max_length=200)
    # 从技术手册表迁移过来的字段
    special_consumables_price: Optional[float] = Field(default=None, description="分析特殊耗材单价")

    # 采样信息
    sample_source: Optional[str] = Field(default=None, description="样品来源", max_length=50)
    point_name: Optional[str] = Field(default=None, description="点位名称", max_length=100)
    point_count: int = Field(default=1, description="点位数")
    cycle_type: Optional[str] = Field(default=None, description="检测周期类型", max_length=10)
    cycle_count: Optional[int] = Field(default=1, description="检测周期数")
    frequency: Optional[int] = Field(default=1, description="检测频次数")
    sample_count: Optional[int] = Field(default=1, description="样品数")
    is_subcontract: Optional[str] = Field(default="0", description="是否分包(0-否,1-是)", max_length=1)

    # 备注
    remark: Optional[str] = Field(default=None, description="备注")

    @field_validator("category")
    @classmethod
    def validate_category(cls, v):
        if not v:
            raise ValueError("检测类别不能为空")
        if len(v) > 50:
            raise ValueError("检测类别长度不能超过50个字符")
        return v

    @field_validator("parameter")
    @classmethod
    def validate_parameter(cls, v):
        if not v:
            raise ValueError("检测参数不能为空")
        if len(v) > 100:
            raise ValueError("检测参数长度不能超过100个字符")
        return v

    @field_validator("method")
    @classmethod
    def validate_method(cls, v):
        if not v:
            raise ValueError("检测方法不能为空")
        if len(v) > 200:
            raise ValueError("检测方法长度不能超过200个字符")
        return v

    @field_validator("is_subcontract")
    @classmethod
    def validate_is_subcontract(cls, v):
        if v is not None and v not in ["0", "1"]:
            raise ValueError("是否分包字段值必须为0或1")
        return v


class ProjectQuotationAttachmentModel(BaseModel):
    """
    项目报价附件模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description="主键ID")
    file_name: str = Field(..., description="文件名称", max_length=100)
    file_path: str = Field(..., description="文件路径", max_length=200)
    file_size: int = Field(..., description="文件大小")
    file_type: Optional[str] = Field(default=None, description="文件类型", max_length=50)

    @field_validator("file_name")
    @classmethod
    def validate_file_name(cls, v):
        if not v:
            raise ValueError("文件名称不能为空")
        if len(v) > 100:
            raise ValueError("文件名称长度不能超过100个字符")
        return v

    @field_validator("file_path")
    @classmethod
    def validate_file_path(cls, v):
        if not v:
            raise ValueError("文件路径不能为空")
        if len(v) > 200:
            raise ValueError("文件路径长度不能超过200个字符")
        return v


class ProjectQuotationOtherFeeModel(BaseModel):
    """
    项目报价其他费用模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description="主键ID")
    fee_name: str = Field(..., description="费用名称", max_length=50)
    quantity: int = Field(default=1, description="数量")
    unit_price: Decimal = Field(default=0, description="单价")
    total_price: Decimal = Field(default=0, description="总价")
    remark: Optional[str] = Field(default=None, description="备注")

    @field_validator("fee_name")
    @classmethod
    def validate_fee_name(cls, v):
        if not v:
            raise ValueError("费用名称不能为空")
        if len(v) > 50:
            raise ValueError("费用名称长度不能超过50个字符")
        return v


class ProjectQuotationTotalFeeModel(BaseModel):
    """
    项目报价总费用模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description="主键ID")
    discount_rate: Decimal = Field(default=100, description="检测项目总折扣率")
    discounted_testing_fee: Decimal = Field(default=0, description="检测折后费用")
    special_consumables_fee: Decimal = Field(default=0, description="特殊耗材总费用")
    other_fee: Decimal = Field(default=0, description="其他费用")
    total_fee_before_discount: Decimal = Field(default=0, description="优惠前总费用")
    tax_rate: Decimal = Field(default=0, description="税率")
    tax: Decimal = Field(default=0, description="税费")
    total_fee_after_tax: Decimal = Field(default=0, description="优惠前总费用(税后)")
    adjustment_amount: Decimal = Field(default=0, description="整体调整金额")
    final_amount: Decimal = Field(default=0, description="优惠后总金额")


class AddProjectQuotationModel(BaseModel):
    """
    新增项目报价模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    project_name: str = Field(..., description="项目名称", max_length=100)
    business_type: str = Field(..., description="业务类别：sampling-一般采样，sample-送样", max_length=20)
    contract_code: Optional[str] = Field(default=None, description="合同编号", max_length=50)
    customer_id: Optional[int] = Field(default=None, description="客户ID")
    customer_name: Optional[str] = Field(default=None, description="客户名称", max_length=100)
    customer_address: Optional[str] = Field(default=None, description="客户地址", max_length=200)
    customer_contact: Optional[str] = Field(default=None, description="客户联系人", max_length=50)
    customer_phone: Optional[str] = Field(default=None, description="客户联系电话", max_length=20)
    inspected_party: Optional[str] = Field(default=None, description="受检方企业名称", max_length=100)
    inspected_contact: Optional[str] = Field(default=None, description="受检方联系人", max_length=50)
    inspected_phone: Optional[str] = Field(default=None, description="受检方联系电话", max_length=20)
    inspected_address: Optional[str] = Field(default=None, description="受检方详细地址", max_length=200)
    project_manager: Optional[str] = Field(default=None, description="项目负责人", max_length=50)
    market_manager: Optional[str] = Field(default=None, description="市场负责人", max_length=50)
    technical_manager: Optional[str] = Field(default=None, description="项目技术人", max_length=50)
    commission_date: Optional[datetime] = Field(default=None, description="委托日期")
    project_province: Optional[str] = Field(default=None, description="项目所在省", max_length=50)
    project_city: Optional[str] = Field(default=None, description="项目所在市", max_length=50)
    # approvers字段已废弃，使用customer_support_ids
    customer_support_ids: Optional[List[int]] = Field(default=[], description="项目客服ID列表")
    status: str = Field(default="0", description="项目审批状态", max_length=1)
    remark: Optional[str] = Field(default=None, description="备注")
    items: List[ProjectQuotationItemModel] = Field(default=[], description="项目报价明细")
    attachments: List[ProjectQuotationAttachmentModel] = Field(default=[], description="项目报价附件")
    other_fees: List[ProjectQuotationOtherFeeModel] = Field(default=[], description="项目报价其他费用")
    total_fee: Optional[ProjectQuotationTotalFeeModel] = Field(default=None, description="项目报价总费用")

    @field_validator("project_name")
    @classmethod
    def validate_project_name(cls, v):
        if not v:
            raise ValueError("项目名称不能为空")
        if len(v) > 100:
            raise ValueError("项目名称长度不能超过100个字符")
        return v

    @field_validator("business_type")
    @classmethod
    def validate_business_type(cls, v):
        if not v:
            raise ValueError("业务类别不能为空")
        if v not in ["sampling", "sample"]:
            raise ValueError("业务类别必须是 sampling（一般采样）或 sample（送样）")
        return v

    @model_validator(mode="after")
    def validate_model(self):
        # 这里可以添加跨字段的验证逻辑
        return self


class EditProjectQuotationModel(BaseModel):
    """
    编辑项目报价模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: int = Field(..., description="主键ID")
    project_name: str = Field(..., description="项目名称", max_length=100)
    project_code: str = Field(..., description="项目编号", max_length=50)
    business_type: str = Field(..., description="业务类别：sampling-一般采样，sample-送样", max_length=20)
    contract_code: Optional[str] = Field(default=None, description="合同编号", max_length=50)
    customer_id: Optional[int] = Field(default=None, description="客户ID")
    customer_name: Optional[str] = Field(default=None, description="客户名称", max_length=100)
    customer_address: Optional[str] = Field(default=None, description="客户地址", max_length=200)
    customer_contact: Optional[str] = Field(default=None, description="客户联系人", max_length=50)
    customer_phone: Optional[str] = Field(default=None, description="客户联系电话", max_length=20)
    inspected_party: Optional[str] = Field(default=None, description="受检方企业名称", max_length=100)
    inspected_contact: Optional[str] = Field(default=None, description="受检方联系人", max_length=50)
    inspected_phone: Optional[str] = Field(default=None, description="受检方联系电话", max_length=20)
    inspected_address: Optional[str] = Field(default=None, description="受检方详细地址", max_length=200)
    project_manager: Optional[str] = Field(default=None, description="项目负责人", max_length=50)
    market_manager: Optional[str] = Field(default=None, description="市场负责人", max_length=50)
    technical_manager: Optional[str] = Field(default=None, description="项目技术人", max_length=50)
    commission_date: Optional[datetime] = Field(default=None, description="委托日期")
    project_province: Optional[str] = Field(default=None, description="项目所在省", max_length=50)
    project_city: Optional[str] = Field(default=None, description="项目所在市", max_length=50)
    # approvers字段已废弃，使用customer_support_ids和customer_support_list
    customer_support_ids: Optional[List[int]] = Field(default=[], description="项目客服ID列表")
    status: str = Field(..., description="项目审批状态", max_length=1)
    remark: Optional[str] = Field(default=None, description="备注")
    items: List[ProjectQuotationItemModel] = Field(default=[], description="项目报价明细")
    attachments: List[ProjectQuotationAttachmentModel] = Field(default=[], description="项目报价附件")
    other_fees: List[ProjectQuotationOtherFeeModel] = Field(default=[], description="项目报价其他费用")
    total_fee: Optional[ProjectQuotationTotalFeeModel] = Field(default=None, description="项目报价总费用")

    @field_validator("id")
    @classmethod
    def validate_id(cls, v):
        if v <= 0:
            raise ValueError("ID必须大于0")
        return v

    @field_validator("project_name")
    @classmethod
    def validate_project_name(cls, v):
        if not v:
            raise ValueError("项目名称不能为空")
        if len(v) > 100:
            raise ValueError("项目名称长度不能超过100个字符")
        return v

    @field_validator("project_code")
    @classmethod
    def validate_project_code(cls, v):
        if not v:
            raise ValueError("项目编号不能为空")
        if len(v) > 50:
            raise ValueError("项目编号长度不能超过50个字符")
        return v

    @field_validator("business_type")
    @classmethod
    def validate_business_type(cls, v):
        if not v:
            raise ValueError("业务类别不能为空")
        if v not in ["sampling", "sample"]:
            raise ValueError("业务类别必须是 sampling（一般采样）或 sample（送样）")
        return v

    @model_validator(mode="after")
    def validate_model(self):
        # 这里可以添加跨字段的验证逻辑
        return self


class ProjectQuotationModel(BaseModel):
    """
    项目报价模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: int = Field(..., description="主键ID")
    project_name: str = Field(..., description="项目名称", max_length=100)
    project_code: str = Field(..., description="项目编号", max_length=50)
    business_type: str = Field(..., description="业务类别：sampling-一般采样，sample-送样", max_length=20)
    contract_code: Optional[str] = Field(default=None, description="合同编号", max_length=50)
    customer_id: Optional[int] = Field(default=None, description="客户ID")
    customer_name: Optional[str] = Field(default=None, description="客户名称", max_length=100)
    customer_address: Optional[str] = Field(default=None, description="客户地址", max_length=200)
    customer_contact: Optional[str] = Field(default=None, description="客户联系人", max_length=50)
    customer_phone: Optional[str] = Field(default=None, description="客户联系电话", max_length=20)
    inspected_party: Optional[str] = Field(default=None, description="受检方企业名称", max_length=100)
    inspected_contact: Optional[str] = Field(default=None, description="受检方联系人", max_length=50)
    inspected_phone: Optional[str] = Field(default=None, description="受检方联系电话", max_length=20)
    inspected_address: Optional[str] = Field(default=None, description="受检方详细地址", max_length=200)
    project_manager: Optional[str] = Field(default=None, description="项目负责人", max_length=50)
    market_manager: Optional[str] = Field(default=None, description="市场负责人", max_length=50)
    technical_manager: Optional[str] = Field(default=None, description="项目技术人", max_length=50)
    commission_date: Optional[datetime] = Field(default=None, description="委托日期")
    project_province: Optional[str] = Field(default=None, description="项目所在省", max_length=50)
    project_city: Optional[str] = Field(default=None, description="项目所在市", max_length=50)
    # approvers字段已废弃，使用customer_support_ids和customer_support_list
    customer_support_ids: Optional[List[int]] = Field(default=[], description="项目客服ID列表")
    customer_support_list: List[ProjectQuotationCustomerSupportModel] = Field(default=[], description="项目客服列表")
    status: str = Field(..., description="项目审批状态", max_length=1)
    remark: Optional[str] = Field(default=None, description="备注")
    create_by: Optional[str] = Field(default=None, description="创建人", max_length=50)
    create_time: Optional[datetime] = Field(default=None, description="创建时间")
    update_by: Optional[str] = Field(default=None, description="更新人", max_length=50)
    update_time: Optional[datetime] = Field(default=None, description="更新时间")
    items: List[ProjectQuotationItemModel] = Field(default=[], description="项目报价明细")
    attachments: List[ProjectQuotationAttachmentModel] = Field(default=[], description="项目报价附件")
    other_fees: List[ProjectQuotationOtherFeeModel] = Field(default=[], description="项目报价其他费用")
    special_consumables_fee: List["ProjectQuotationSpecialConsumableFeeModel"] = Field(
        default=[], description="项目报价特殊耗材费用"
    )
    total_fee: Optional[ProjectQuotationTotalFeeModel] = Field(default=None, description="项目报价总费用")


class ProjectQuotationQueryModel(BaseModel):
    """
    项目报价查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    project_name: Optional[str] = Field(default=None, description="项目名称")
    project_code: Optional[str] = Field(default=None, description="项目编号")
    customer_name: Optional[str] = Field(default=None, description="客户名称")
    business_type: Optional[str] = Field(default=None, description="业务类别")
    status: Optional[str] = Field(default=None, description="项目审批状态")
    begin_time: Optional[str] = Field(default=None, description="开始时间")
    end_time: Optional[str] = Field(default=None, description="结束时间")


class ProjectQuotationPageQueryModel(ProjectQuotationQueryModel):
    """
    项目报价分页查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    page_num: int = Field(default=1, description="页码")
    page_size: int = Field(default=10, description="每页条数")


class ProjectQuotationItemBasedataPriceModel(BaseModel):
    """
    项目报价明细基础价目表模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: Optional[int] = Field(default=None, description="主键ID")
    project_quotation_id: int = Field(..., description="项目报价ID")
    project_quotation_item_id: str = Field(..., description="项目明细的ID", max_length=20)

    # 以下字段完全来源于 technical_manual_price 表
    method: str = Field(..., description="检测方法", max_length=100)
    category: str = Field(..., description="检测类别", max_length=50)
    classification: Optional[str] = Field(default=None, description="分类", max_length=50)
    first_item_price: Optional[Decimal] = Field(default=None, description="检测首项单价")
    additional_item_price: Optional[Decimal] = Field(default=None, description="检测增项单价")
    testing_fee_limit: Optional[Decimal] = Field(default=None, description="检测费上限")
    sampling_price: Optional[Decimal] = Field(default=None, description="采集单价")
    pretreatment_price: Optional[Decimal] = Field(default=None, description="前处理单价")
    special_consumables_price: Optional[Decimal] = Field(default=None, description="分析特殊耗材单价")

    # 基础字段
    create_by: Optional[str] = Field(default=None, description="创建者", max_length=64)
    create_time: Optional[datetime] = Field(default=None, description="创建时间")
    remark: Optional[str] = Field(default=None, description="备注", max_length=500)


class ProjectQuotationFeeSummaryModel(BaseModel):
    """
    项目报价费用汇总模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)
    sampling_fee: float = Field(default=0.0, description="采样费用")
    testing_fee: float = Field(default=0.0, description="检测费用")
    pretreatment_fee: float = Field(default=0.0, description="前处理费用")
    special_consumables_fee: float = Field(default=0.0, description="特殊耗材费")
    total_fee: float = Field(default=0.0, description="总费用")


class ProjectQuotationFeeCalculationResultModel(BaseModel):
    """
    项目报价费用计算结果模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    details: Optional[List] = Field(default=None, description="计算详情")
    summary: ProjectQuotationFeeSummaryModel = Field(
        description="费用汇总",
        example={
            "sampling_fee": 0.0,
            "testing_fee": 0.0,
            "pretreatment_fee": 0.0,
            "special_consumables_fee": 0.0,
            "total_fee": 0.0,
        },
    )


class ProjectQuotationFeeCalculationModel(BaseModel):
    """
    项目报价费用计算结果模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    # 费用明细
    sampling_fee: Decimal = Field(default=Decimal("0"), description="采样费用")
    testing_fee: Decimal = Field(default=Decimal("0"), description="检测费用")
    pretreatment_fee: Decimal = Field(default=Decimal("0"), description="前处理费用")
    special_consumables_fee: Decimal = Field(default=Decimal("0"), description="特殊耗材费")
    total_fee: Decimal = Field(default=Decimal("0"), description="汇总费用")

    # 计算详情
    calculation_details: Optional[ProjectQuotationFeeCalculationResultModel] = Field(
        default=None, description="计算详情"
    )


class ProjectQuotationItemImportModel(BaseModel):
    """
    项目报价明细导入模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    classification: Optional[str] = Field(default=None, description="分类")
    category: str = Field(..., description="二级分类")
    parameter: str = Field(..., description="指标")
    method: str = Field(..., description="方法")
    sample_source: Optional[str] = Field(default=None, description="样品来源")
    point_name: Optional[str] = Field(default=None, description="点位名称")
    point_count: Optional[str] = Field(default="1", description="点位数")
    cycle_type: Optional[str] = Field(default=None, description="周期类型")
    cycle_count: Optional[str] = Field(default="1", description="检测周期数")
    frequency: Optional[str] = Field(default="1", description="检测频次数")
    sample_count: Optional[str] = Field(default="1", description="样品数")
    is_subcontract: Optional[str] = Field(default="否", description="是否分包")
    remark: Optional[str] = Field(default=None, description="备注")


class ProjectQuotationItemImportErrorModel(BaseModel):
    """
    项目报价明细导入错误模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    row_number: Optional[int] = Field(default=0, description="错误行号")
    error_message: Optional[str] = Field(None, description="错误原因")
    classification: Optional[str] = Field(default=None, description="分类")
    category: Optional[str] = Field(default=None, description="二级分类")
    parameter: Optional[str] = Field(default=None, description="指标")
    method: Optional[str] = Field(default=None, description="方法")
    sample_source: Optional[str] = Field(default=None, description="样品来源")
    point_name: Optional[str] = Field(default=None, description="点位名称")
    point_count: Optional[str] = Field(default=None, description="点位数")
    cycle_type: Optional[str] = Field(default=None, description="周期类型")
    cycle_count: Optional[str] = Field(default=None, description="检测周期数")
    frequency: Optional[str] = Field(default=None, description="检测频次数")
    sample_count: Optional[str] = Field(default=None, description="样品数")
    is_subcontract: Optional[str] = Field(default=None, description="是否分包")
    remark: Optional[str] = Field(default=None, description="备注")


class ProjectQuotationItemImportResultModel(BaseModel):
    """
    项目报价明细导入结果模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    success: bool = Field(..., description="是否成功")
    total_count: int = Field(..., description="总记录数")
    success_count: int = Field(..., description="成功记录数")
    error_count: int = Field(..., description="错误记录数")
    errors: List[ProjectQuotationItemImportErrorModel] = Field(default=[], description="错误列表")
    data: List[ProjectQuotationItemModel] = Field(default=[], description="成功导入的数据")


# 导入特殊耗材模型（避免循环导入）
from module_quotation.entity.vo.project_quotation_special_consumable_vo import ProjectQuotationSpecialConsumableFeeModel
