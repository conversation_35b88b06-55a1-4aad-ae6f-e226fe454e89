#!/usr/bin/env python3
"""
添加采样任务表缺失字段的迁移脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from config.get_db import get_db


async def add_sampling_task_fields():
    """添加采样任务表缺失的字段"""
    
    # 获取数据库会话
    async for db in get_db():
        try:
            print("🔄 开始添加采样任务表字段...")
            
            # 检查字段是否已存在
            check_sql = """
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                  AND TABLE_NAME = 'sampling_task' 
                  AND COLUMN_NAME IN ('sampling_location', 'sampling_method', 'sample_count', 'remarks')
            """
            
            result = await db.execute(text(check_sql))
            existing_columns = [row[0] for row in result.fetchall()]
            
            print(f"📋 已存在的字段: {existing_columns}")
            
            # 需要添加的字段
            fields_to_add = []
            if 'sampling_location' not in existing_columns:
                fields_to_add.append("ADD COLUMN sampling_location VARCHAR(500) COMMENT '采样地点'")
            if 'sampling_method' not in existing_columns:
                fields_to_add.append("ADD COLUMN sampling_method VARCHAR(200) COMMENT '采样方法'")
            if 'sample_count' not in existing_columns:
                fields_to_add.append("ADD COLUMN sample_count INT COMMENT '样品数量'")
            if 'remarks' not in existing_columns:
                fields_to_add.append("ADD COLUMN remarks TEXT COMMENT '备注'")
            
            if not fields_to_add:
                print("✅ 所有字段都已存在，无需添加")
                return
            
            # 执行添加字段的SQL
            alter_sql = f"ALTER TABLE sampling_task {', '.join(fields_to_add)}"
            print(f"🔧 执行SQL: {alter_sql}")
            
            await db.execute(text(alter_sql))
            await db.commit()
            
            print("✅ 字段添加成功！")
            
            # 验证字段是否添加成功
            verify_sql = """
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                  AND TABLE_NAME = 'sampling_task' 
                  AND COLUMN_NAME IN ('sampling_location', 'sampling_method', 'sample_count', 'remarks')
                ORDER BY ORDINAL_POSITION
            """
            
            result = await db.execute(text(verify_sql))
            columns = result.fetchall()
            
            print("\n📋 验证结果:")
            for column in columns:
                print(f"  - {column[0]}: {column[1]} ({column[3]})")
            
        except Exception as e:
            print(f"❌ 添加字段失败: {str(e)}")
            await db.rollback()
            raise
        finally:
            await db.close()
        break


if __name__ == "__main__":
    asyncio.run(add_sampling_task_fields())
