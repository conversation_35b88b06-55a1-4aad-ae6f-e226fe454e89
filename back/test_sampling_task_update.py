#!/usr/bin/env python3
"""
测试采样任务更新接口
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from config.get_db import get_db
from module_sampling.service.sampling_task_service import SamplingTaskService
from module_sampling.dto.sampling_task_dto import SamplingTaskUpdateDTO


async def test_sampling_task_update():
    """测试采样任务更新功能"""
    
    # 获取数据库会话
    async for db in get_db():
        try:
            print("🔄 开始测试采样任务更新...")
            
            # 首先查看有哪些采样任务
            query_sql = """
                SELECT id, task_name, task_code, status, description
                FROM sampling_task 
                ORDER BY id 
                LIMIT 5
            """
            
            result = await db.execute(text(query_sql))
            tasks = result.fetchall()
            
            print(f"📋 现有采样任务:")
            for task in tasks:
                print(f"  - ID: {task[0]}, 名称: {task[1]}, 编号: {task[2]}, 状态: {task[3]}")
            
            if not tasks:
                print("❌ 没有找到采样任务，无法测试更新功能")
                return
            
            # 选择第一个任务进行测试
            task_id = tasks[0][0]
            print(f"\n🎯 选择任务ID {task_id} 进行测试")
            
            # 创建更新DTO
            update_dto = SamplingTaskUpdateDTO(
                task_name="测试更新任务名称",
                description="测试更新描述",
                sampling_location="测试采样地点",
                sampling_method="测试采样方法",
                sample_count=10,
                remarks="测试备注信息"
            )
            
            print(f"📝 更新数据: {update_dto.model_dump()}")
            
            # 执行更新
            service = SamplingTaskService(db)
            result = await service.update_sampling_task(task_id, update_dto, 1)  # 使用用户ID 1
            
            print(f"✅ 更新成功！结果: {result}")
            
            # 验证更新结果
            verify_sql = """
                SELECT task_name, description, sampling_location, sampling_method, sample_count, remarks
                FROM sampling_task 
                WHERE id = :task_id
            """
            
            verify_result = await db.execute(text(verify_sql), {"task_id": task_id})
            updated_task = verify_result.fetchone()
            
            print(f"\n📋 验证更新结果:")
            if updated_task:
                print(f"  - 任务名称: {updated_task[0]}")
                print(f"  - 描述: {updated_task[1]}")
                print(f"  - 采样地点: {updated_task[2]}")
                print(f"  - 采样方法: {updated_task[3]}")
                print(f"  - 样品数量: {updated_task[4]}")
                print(f"  - 备注: {updated_task[5]}")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            await db.rollback()
        finally:
            await db.close()
        break


if __name__ == "__main__":
    asyncio.run(test_sampling_task_update())
