"""
测试新的项目报价数据结构
"""

import json
from datetime import datetime
from decimal import Decimal

# 模拟前端发送的新数据结构
test_data = {
    "projectName": "环境监测项目",
    "contractCode": "HT2024001",
    "serviceType": "",
    "customerName": "测试客户",
    "customerAddress": "测试地址",
    "customerContact": "张三",
    "customerPhone": "13800138000",
    "inspectedParty": "被检方企业",
    "inspectedContact": "李四",
    "inspectedPhone": "13900139000",
    "inspectedAddress": "被检方地址",
    "projectManager": "项目经理",
    "marketManager": "市场经理",
    "technicalManager": "技术经理",
    "commissionDate": "2025-01-26",
    "approverIds": [1, 2],  # 审批人ID列表
    "status": "0",
    "remark": "测试备注",
    "items": [
        {
            "category": "环境空气和废气",
            "classification": "气",
            "parameter": "1,1-二氯乙烷",
            "method": "环境空气 挥发性卤代烃的测定 活性炭吸附-二硫化碳解吸/气相色谱法 HJ 645-2013",
            "limitationScope": "检测限制范围",
            "remark": "项目备注",
            "pointName": "废气排放口",
            "pointCount": 1,
            "cycleType": "日",
            "cycleCount": 1,
            "frequency": 1,
            "sampleCount": 1,
            "sampleSource": "现场采集",
            "isSubcontract": "0"
        },
        {
            "category": "水和废水",
            "classification": "水",
            "parameter": "pH值",
            "method": "玻璃电极法 GB 6920-86",
            "limitationScope": "6.0-9.0",
            "remark": "重要指标",
            "pointName": "进水口",
            "pointCount": 2,
            "cycleType": "周",
            "cycleCount": 4,
            "frequency": 2,
            "sampleCount": 3,
            "sampleSource": "客户提供",
            "isSubcontract": "1"
        }
    ],
    "attachments": []
}

def test_data_structure():
    """
    测试数据结构是否符合预期
    """
    print("=== 测试新的项目报价数据结构 ===")
    print(f"项目名称: {test_data['projectName']}")
    print(f"客户名称: {test_data['customerName']}")
    print(f"报价明细数量: {len(test_data['items'])}")
    
    print("\n=== 报价明细信息 ===")
    for i, item in enumerate(test_data['items'], 1):
        print(f"\n明细 {i}:")
        print(f"  分类: {item['classification']}")
        print(f"  二级分类: {item['category']}")
        print(f"  指标: {item['parameter']}")
        print(f"  方法: {item['method']}")
        print(f"  限制范围: {item['limitationScope']}")
        print(f"  样品来源: {item['sampleSource']}")
        print(f"  点位名称: {item['pointName']}")
        print(f"  点位数: {item['pointCount']}")
        print(f"  检测周期: {item['cycleType']}")
        print(f"  周期数: {item['cycleCount']}")
        print(f"  频次: {item['frequency']}")
        print(f"  样品数: {item['sampleCount']}")
        print(f"  是否分包: {'是' if item['isSubcontract'] == '1' else '否'}")
        print(f"  备注: {item['remark']}")

def validate_required_fields():
    """
    验证必填字段
    """
    print("\n=== 验证必填字段 ===")
    
    # 项目基本信息必填字段
    required_project_fields = ['projectName']
    for field in required_project_fields:
        if not test_data.get(field):
            print(f"❌ 项目字段 {field} 为空")
        else:
            print(f"✅ 项目字段 {field} 已填写")
    
    # 报价明细必填字段
    required_item_fields = ['category', 'parameter', 'method']
    for i, item in enumerate(test_data['items'], 1):
        print(f"\n明细 {i} 字段验证:")
        for field in required_item_fields:
            if not item.get(field):
                print(f"  ❌ {field} 为空")
            else:
                print(f"  ✅ {field} 已填写")

def generate_sql_insert():
    """
    生成对应的SQL插入语句示例
    """
    print("\n=== 生成SQL插入语句示例 ===")
    
    # 项目报价主表
    print("-- 项目报价主表")
    print(f"""
INSERT INTO project_quotation (
    project_name, contract_code, service_type, customer_name, 
    customer_address, customer_contact, customer_phone,
    inspected_party, inspected_contact, inspected_phone, inspected_address,
    project_manager, market_manager, technical_manager,
    commission_date, status, remark,
    create_time, update_time
) VALUES (
    '{test_data['projectName']}', '{test_data['contractCode']}', '{test_data['serviceType']}', '{test_data['customerName']}',
    '{test_data['customerAddress']}', '{test_data['customerContact']}', '{test_data['customerPhone']}',
    '{test_data['inspectedParty']}', '{test_data['inspectedContact']}', '{test_data['inspectedPhone']}', '{test_data['inspectedAddress']}',
    '{test_data['projectManager']}', '{test_data['marketManager']}', '{test_data['technicalManager']}',
    '{test_data['commissionDate']}', '{test_data['status']}', '{test_data['remark']}',
    NOW(), NOW()
);

-- 插入审批人关联数据
INSERT INTO project_quotation_approver (project_quotation_id, user_id, create_time, update_time)
VALUES 
    (1, {test_data['approverIds'][0]}, NOW(), NOW()),
    (1, {test_data['approverIds'][1]}, NOW(), NOW())
""")
    
    # 项目报价明细表
    print("-- 项目报价明细表")
    for i, item in enumerate(test_data['items'], 1):
        print(f"""
INSERT INTO project_quotation_item (
    project_quotation_id, item_code, classification, category, parameter, method,
    limitation_scope, sample_source, point_name, point_count,
    cycle_type, cycle_count, frequency, sample_count, is_subcontract, remark,
    create_time, update_time
) VALUES (
    1, 'ITEM{i:03d}', '{item['classification']}', '{item['category']}', '{item['parameter']}', '{item['method']}',
    '{item['limitationScope']}', '{item['sampleSource']}', '{item['pointName']}', {item['pointCount']},
    '{item['cycleType']}', {item['cycleCount']}, {item['frequency']}, {item['sampleCount']}, '{item['isSubcontract']}', '{item['remark']}',
    NOW(), NOW()
);""")

if __name__ == "__main__":
    test_data_structure()
    validate_required_fields()
    generate_sql_insert()
    
    print("\n=== 测试完成 ===")
    print("新的数据结构已验证，符合前端发送的格式要求。")
