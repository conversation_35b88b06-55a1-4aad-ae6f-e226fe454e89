-- 添加采样任务表缺失的字段
-- 执行时间: 2025-08-03

-- 添加采样信息相关字段
ALTER TABLE sampling_task 
ADD COLUMN sampling_location VARCHAR(500) COMMENT '采样地点' AFTER actual_end_date,
ADD COLUMN sampling_method VARCHAR(200) COMMENT '采样方法' AFTER sampling_location,
ADD COLUMN sample_count INT COMMENT '样品数量' AFTER sampling_method,
ADD COLUMN remarks TEXT COMMENT '备注' AFTER sample_count;

-- 添加索引（可选）
CREATE INDEX idx_sampling_task_sampling_location ON sampling_task(sampling_location);
CREATE INDEX idx_sampling_task_sampling_method ON sampling_task(sampling_method);

-- 验证字段是否添加成功
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'sampling_task' 
  AND COLUMN_NAME IN ('sampling_location', 'sampling_method', 'sample_count', 'remarks')
ORDER BY ORDINAL_POSITION;
