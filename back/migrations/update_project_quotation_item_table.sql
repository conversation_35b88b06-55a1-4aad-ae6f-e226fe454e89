-- 更新项目报价明细表结构
-- 适配新的前端数据结构

-- 1. 添加新字段
ALTER TABLE project_quotation_item 
ADD COLUMN classification VARCHAR(50) COMMENT '分类' AFTER item_code,
ADD COLUMN limitation_scope VARCHAR(200) COMMENT '限制范围' AFTER method,
ADD COLUMN sample_source VARCHAR(50) COMMENT '样品来源' AFTER limitation_scope,
ADD COLUMN is_subcontract VARCHAR(1) DEFAULT '0' COMMENT '是否分包(0-否,1-是)' AFTER sample_count;

-- 2. 修改现有字段
ALTER TABLE project_quotation_item 
MODIFY COLUMN parameter VARCHAR(100) NOT NULL COMMENT '检测参数(指标)',
MODIFY COLUMN method VARCHAR(200) NOT NULL COMMENT '检测方法',
MODIFY COLUMN category VARCHAR(50) NOT NULL COMMENT '检测类别(二级分类)',
MODIFY COLUMN cycle_type VARCHAR(10) COMMENT '检测周期类型',
MODIFY COLUMN cycle_count INT DEFAULT 1 COMMENT '检测周期数',
MODIFY COLUMN frequency INT DEFAULT 1 COMMENT '检测频次数';

-- 3. 删除不再需要的字段
ALTER TABLE project_quotation_item 
DROP COLUMN service_type,
DROP COLUMN test_code,
DROP COLUMN price_code,
DROP COLUMN sampling_price,
DROP COLUMN testing_price,
DROP COLUMN travel_price,
DROP COLUMN total_price;

-- 4. 添加索引
CREATE INDEX idx_project_quotation_item_classification ON project_quotation_item(classification);
CREATE INDEX idx_project_quotation_item_category ON project_quotation_item(category);
CREATE INDEX idx_project_quotation_item_parameter ON project_quotation_item(parameter);
CREATE INDEX idx_project_quotation_item_sample_source ON project_quotation_item(sample_source);

-- 5. 更新表注释
ALTER TABLE project_quotation_item COMMENT = '项目报价明细表 - 存储报价明细信息，包括检测项目和采样信息';
