# 报价明细组件数据保持功能测试

## 测试场景
验证报价明细配置组件在多次打开/关闭时能够保持之前配置的数据。

## 测试步骤

### 1. 初次配置报价明细
1. 打开新增项目报价对话框
2. 进入第二步"报价明细配置"
3. 点击"配置报价明细"按钮
4. 添加检测类别（如：水质检测）
5. 添加检测项目（如：pH值、溶解氧等）
6. 为检测项目配置采样点位
7. 点击确认，关闭报价明细配置

### 2. 验证数据保持
1. 再次点击"配置报价明细"按钮
2. 验证之前配置的数据是否还在：
   - 检测类别标签页是否存在
   - 检测项目是否还在表格中
   - 采样点位数据是否保持

### 3. 继续编辑
1. 在现有基础上添加新的检测类别
2. 添加更多检测项目
3. 配置更多采样点位
4. 确认保存

### 4. 最终验证
1. 再次打开报价明细配置
2. 验证所有数据（包括新增的）都被保持
3. 检查生成的扁平化数据结构是否正确

## 预期结果
- ✅ 每次打开报价明细配置时，之前的数据都应该保持
- ✅ 可以在现有数据基础上继续编辑
- ✅ 生成的数据结构包含正确的字段名
- ✅ 表格显示的多级表头结构正确

## 数据结构验证
生成的扁平化数据应包含以下字段：
```javascript
{
  // 检测项目信息
  classification: "分类",
  category: "二级分类", 
  parameter: "指标",
  method: "方法",
  remark: "备注",
  
  // 采样信息
  sampleSource: "样品来源",
  pointName: "点位名称",
  pointCount: 1,
  cycleType: "检测周期类型",
  cycleCount: 1,
  frequency: 1,
  sampleCount: 1,
  isSubcontract: false
}
```
