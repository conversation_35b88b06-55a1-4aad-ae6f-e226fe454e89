<template>
  <div class="sampling-point-arrangement">
    <el-dialog
      :title="title"
      v-model="dialogVisible"
      width="600px"
      append-to-body
      @close="handleClose"
    >
      <div class="arrangement-content">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="180px"
          label-position="right"
        >
          <el-form-item label="点位名称" prop="pointName">
            <el-input
              v-model="form.pointName"
              placeholder="请输入采样的点位名称"
              clearable
              style="width: 50%;"
            />
          </el-form-item>
          
          <el-form-item label="点位数" prop="pointCount">
            <el-input-number
              v-model="form.pointCount"
              :min="1"
              :max="9999"
              placeholder="请输入采样的点位数量"
              style="width: 50%;"
            />
            <div class="field-tip">
              <el-icon><InfoFilled /></el-icon>
              一个采样点位的单次样品数量
            </div>
          </el-form-item>
          
          <el-form-item label="检测周期类型" prop="cycleType">
            <el-radio-group v-model="form.cycleType">
              <el-radio label="日">日</el-radio>
              <el-radio label="周">周</el-radio>
              <el-radio label="月">月</el-radio>
              <el-radio label="季">季</el-radio>
              <el-radio label="年">年</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="检测周期数" prop="cycleCount">
            <el-input-number
              v-model="form.cycleCount"
              :min="1"
              :max="9999"
              placeholder="请输入检测周期数"
              style="width: 50%;"
            />
          </el-form-item>
          
          <el-form-item label="检测频次(次/检测周期)" prop="frequency">
            <el-input-number
              v-model="form.frequency"
              :min="1"
              :max="9999"
              placeholder="请输入检测频次"
              style="width: 50%;"
            />
            <div class="field-tip">
              <el-icon><InfoFilled /></el-icon>
              每检测周期的采样频次
            </div>
          </el-form-item>
          
          <el-form-item label="样品数" prop="sampleCount">
            <el-input-number
              v-model="form.sampleCount"
              :min="1"
              :max="9999"
              placeholder="请输入样品数"
              style="width: 50%;"
            />
            <div class="field-tip">
              <el-icon><InfoFilled /></el-icon>
              每次采样涉及的样品数量
            </div>
          </el-form-item>
          <el-form-item label="样品来源" prop="sampleSource">
              <el-radio-group v-model="form.sampleSource">
                <el-radio label="采样" value="采样" />
                <el-radio label="送样" value="送样" />
              </el-radio-group>
              <div class="field-tip">
                <el-icon><InfoFilled /></el-icon>
                采样点位下的各指标的样品来源
              </div>
          </el-form-item>
          <el-form-item label="分包方式" prop="isSubcontract">
              <el-radio-group v-model="form.isSubcontract">
                <el-radio label="不分包" value="0" />
                <el-radio label="分包" value="1" />
              </el-radio-group>
              <div class="field-tip">
                <el-icon><InfoFilled /></el-icon>
                为每个采样点位下的指标选择是否分包
              </div>
          </el-form-item>
          
        </el-form>
        
        
        <!-- 预览信息 -->
        <div v-if="isFormValid" class="preview-info">
          <el-divider content-position="left">采样安排预览</el-divider>
          <div class="preview-content">
            <p><strong>点位名称：</strong>{{ form.pointName }}</p>
            <p><strong>采样安排：</strong>{{ form.pointCount }} 个点位，{{ form.cycleCount }} 个{{ form.cycleType }}检测周期</p>
            <p><strong>采样频次：</strong>每{{ form.cycleType }}采样 {{ form.frequency }} 次</p>
            <p><strong>样品总数：</strong>{{ totalSamples }} 个样品</p>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">放 弃</el-button>
          <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '安排采样点位'
  },
  initialData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

// 对话框可见性
const dialogVisible = ref(false)
// 表单引用
const formRef = ref(null)

// 表单数据
const form = ref({
  pointName: '',
  pointCount: 1,
  cycleType: '日',
  cycleCount: 1,
  frequency: 1,
  sampleCount: 1,
  sampleSource: "采样",
  isSubcontract: "0",
})

// 表单验证规则
const rules = ref({
  pointName: [
    { required: true, message: '请输入点位名称', trigger: 'blur' },
    { min: 1, max: 100, message: '点位名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  pointCount: [
    { required: true, message: '请输入点位数', trigger: 'blur' },
    { type: 'number', min: 1, message: '点位数必须大于0', trigger: 'blur' }
  ],
  cycleType: [
    { required: true, message: '请选择检测周期类型', trigger: 'change' }
  ],
  cycleCount: [
    { required: true, message: '请输入检测周期数', trigger: 'blur' },
    { type: 'number', min: 1, message: '检测周期数必须大于0', trigger: 'blur' }
  ],
  frequency: [
    { required: true, message: '请输入检测频次', trigger: 'blur' },
    { type: 'number', min: 1, message: '检测频次必须大于0', trigger: 'blur' }
  ],
  sampleCount: [
    { required: true, message: '请输入样品数', trigger: 'blur' },
    { type: 'number', min: 1, message: '样品数必须大于0', trigger: 'blur' }
  ],
  sampleSource: [
    { required: true, message: '请选择样品来源', trigger: 'change' }
  ],
  isSubcontract: [
    { required: true, message: '请选择分包类型', trigger: 'change' }
  ],
})

// 计算属性：表单是否有效
const isFormValid = computed(() => {
  return form.value.pointName && 
         form.value.pointCount > 0 && 
         form.value.cycleType && 
         form.value.cycleCount > 0 && 
         form.value.frequency > 0 && 
         form.value.sampleCount > 0
})

// 计算属性：样品总数
const totalSamples = computed(() => {
  if (!isFormValid.value) return 0
  return form.value.pointCount * form.value.cycleCount * form.value.frequency * form.value.sampleCount
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    initForm()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 监听初始数据变化
watch(() => props.initialData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.assign(form.value, {
      pointName: '',
      pointCount: 1,
      cycleType: '日',
      cycleCount: 1,
      frequency: 1,
      sampleCount: 1,
      sampleSource: "采样",
      isSubcontract: "0",
      ...newVal
    })
  }
}, { immediate: true, deep: true })

// 初始化表单
function initForm() {
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 设置初始值
  form.value = {
    pointName: '',
    pointCount: 1,
    cycleType: '日',
    cycleCount: 1,
    frequency: 1,
    sampleCount: 1,
    sampleSource: "采样",
    isSubcontract: "0",
    ...props.initialData
  }
}

// 处理确认
function handleConfirm() {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      // 构建返回数据
      const result = {
        ...form.value,
        totalSamples: totalSamples.value
      }
      console.log("采样点位:", result)
      emit('confirm', result)
      ElMessage.success('采样点位安排完成')
      handleClose()
    } else {
      ElMessage.warning('请完善表单信息')
    }
  })
}

// 处理关闭
function handleClose() {
  dialogVisible.value = false
  emit('update:visible', false)
}
</script>

<style scoped>
.sampling-point-arrangement {
  width: 100%;
}

.arrangement-content {
  padding: 10px 0;
}

.field-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.preview-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
}

.preview-content p {
  margin: 8px 0;
  color: #303133;
  line-height: 1.5;
}

.preview-content strong {
  color: #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 表单样式调整 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

:deep(.el-radio) {
  margin-right: 0;
}
</style>
