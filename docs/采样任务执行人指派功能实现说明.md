# 采样任务执行人指派功能实现说明

## 功能概述

根据需求文档 `docs/采样任务分配.md`，我们实现了完整的采样任务执行人指派功能，包括：

1. **任务详情页面聚合显示** - 修改任务详情页面的检测周期条目展示，根据周期序号、周期类型、检测类别和点位名称聚合，并合并单元格
2. **执行人指派功能** - 在任务详情列表新增执行人指派按钮，实现执行人设置功能
3. **采样执行列表页面** - 新增采样执行列表，提供执行人视角的任务列表页面

## 功能区分说明

为了避免命名混淆，我们对相关功能进行了明确区分：

- **采样任务创建** (`/sampling/task-creation`) - 原有的"采样任务分配"功能，实际是从检测周期条目创建采样任务
- **执行人指派** (`/sampling/executor-assignment`) - 新实现的功能，为已创建的采样任务分配执行人

## 技术实现

### 后端实现

#### 数据模型
- `back/module_sampling/entity/do/sampling_task_assignment_do.py` - 采样任务执行人指派数据模型
- `back/module_sampling/dto/sampling_task_assignment_dto.py` - 采样任务执行人指派DTO

#### 数据访问层
- `back/module_sampling/dao/sampling_task_assignment_dao.py` - 采样任务执行人指派DAO

#### 服务层
- `back/module_sampling/service/task_assignment_service.py` - 采样任务执行人指派服务

#### 控制器
- `back/module_sampling/controller/task_assignment_controller.py` - 采样任务执行人指派控制器
- `back/module_sampling/controller/sampling_task_assignment_controller.py` - 采样任务创建控制器（原有功能）

#### 数据库迁移
- `back/migrations/create_sampling_task_executor_assignment_table.sql` - 创建采样任务执行人指派表的SQL脚本

### 前端实现

#### API接口
- `front/src/api/sampling/executorAssignment.js` - 执行人指派相关API

#### 页面组件
- `front/src/views/sampling/task/index.vue` - 修改了采样任务管理页面，添加了聚合显示和执行人指派功能
- `front/src/views/sampling/execution/index.vue` - 新增采样执行列表页面
- `front/src/views/sampling/assignment/index.vue` - 原有的采样任务创建页面

#### 路由配置
- `front/src/router/modules/sampling.js` - 添加了采样执行页面路由，更新了任务创建页面标题

## 功能详细说明

### 1. 任务详情页面聚合显示

- **聚合逻辑**: 根据周期序号、周期类型、检测类别和点位名称进行分组
- **单元格合并**: 使用Element Plus的span-method实现表格单元格合并
- **数据结构**: 在前端对检测周期条目进行聚合处理，生成带有合并信息的数据结构

### 2. 执行人指派功能

- **指派按钮**: 在聚合后的表格行中显示"指派执行人"按钮
- **执行人选择**: 支持多选执行人
- **数据存储**: 将指派信息存储在 `sampling_task_executor_assignment` 表中
- **API接口**:
  - `POST /sampling/executor-assignment/assign` - 创建或更新执行人指派
  - `GET /sampling/executor-assignment/task/{task_id}` - 获取任务的所有执行人指派
  - `GET /sampling/executor-assignment/user/{user_id}` - 获取用户的所有执行任务
  - `DELETE /sampling/executor-assignment/{assignment_id}` - 删除执行人指派

### 3. 采样执行列表页面

- **执行人视角**: 显示当前用户被指派的所有采样任务
- **任务信息**: 显示任务基本信息、周期信息、检测类别等
- **状态管理**: 支持任务状态的查看和管理

## 数据库设计

### 新增表：sampling_task_executor_assignment

```sql
CREATE TABLE IF NOT EXISTS `sampling_task_executor_assignment` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    `sampling_task_id` BIGINT NOT NULL COMMENT '采样任务ID',
    `cycle_number` INT NOT NULL COMMENT '周期序号',
    `cycle_type` VARCHAR(50) COMMENT '周期类型',
    `detection_category` VARCHAR(100) COMMENT '检测类别',
    `point_name` VARCHAR(200) COMMENT '点位名称',
    `assigned_user_ids` TEXT COMMENT '分配的执行人ID列表（JSON格式）',
    `create_by` BIGINT COMMENT '创建人',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` BIGINT COMMENT '更新人',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_sampling_task_executor_assignment_task_id` (`sampling_task_id`),
    INDEX `idx_sampling_task_executor_assignment_cycle` (`cycle_number`, `cycle_type`, `detection_category`, `point_name`),
    FOREIGN KEY (`sampling_task_id`) REFERENCES `sampling_task`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`create_by`) REFERENCES `sys_user`(`user_id`),
    FOREIGN KEY (`update_by`) REFERENCES `sys_user`(`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采样任务执行人指派表';
```

### 修改的表关系

- 在 `sampling_task` 表中添加了与 `sampling_task_executor_assignment` 的关系

## API接口说明

### 执行人指派相关接口

1. **创建或更新执行人指派**
   - 路径: `POST /sampling/executor-assignment/assign`
   - 功能: 为指定的任务周期组合分配执行人

2. **获取任务的执行人指派**
   - 路径: `GET /sampling/executor-assignment/task/{task_id}`
   - 功能: 获取指定任务的所有执行人指派信息

3. **获取用户的执行任务**
   - 路径: `GET /sampling/executor-assignment/user/{user_id}`
   - 功能: 获取指定用户被指派的所有执行任务

4. **删除执行人指派**
   - 路径: `DELETE /sampling/executor-assignment/{assignment_id}`
   - 功能: 删除指定的执行人指派记录

## 使用说明

1. **查看任务详情**: 在采样任务管理页面点击任务详情，可以看到聚合后的检测周期条目
2. **指派执行人**: 在聚合行中点击"指派执行人"按钮，选择执行人并提交
3. **查看执行任务**: 执行人可以在采样执行列表页面查看自己被指派的任务

## 注意事项

1. 执行人指派是基于任务的周期组合进行的，同一个周期组合只能有一个指派记录
2. 指派信息会实时更新，修改指派会覆盖之前的设置
3. 删除任务时会级联删除相关的执行人指派记录
4. 前端使用CamelCase命名规范，后端会自动进行转换
